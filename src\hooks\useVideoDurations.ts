import { useState, useEffect } from 'react';

interface VideoClipData {
  src: string;
  durationInFrames: number;
  durationInSeconds: number;
  name: string;
  loaded: boolean;
}

const initialVideoClips = [
  {
    src: '/videos/clip1.mp4',
    name: 'Clip 1'
  },
  {
    src: '/videos/clip2.mp4',
    name: 'Clip 2'
  },
  {
    src: '/videos/clip3.mp4',
    name: 'Clip 3'
  }
];

export const useVideoDurations = (fps: number = 30) => {
  const [videoClips, setVideoClips] = useState<VideoClipData[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalDurationInFrames, setTotalDurationInFrames] = useState(900); // Default fallback

  const getVideoDuration = (src: string): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';
      
      video.onloadedmetadata = () => {
        const duration = video.duration;
        resolve(duration);
      };
      
      video.onerror = () => {
        reject(new Error(`Failed to load video: ${src}`));
      };
      
      video.src = src;
    });
  };

  useEffect(() => {
    const loadAllVideoDurations = async () => {
      setLoading(true);
      
      const updatedClips = await Promise.all(
        initialVideoClips.map(async (clip) => {
          try {
            const durationInSeconds = await getVideoDuration(clip.src);
            const durationInFrames = Math.round(durationInSeconds * fps);
            
            return {
              ...clip,
              durationInSeconds,
              durationInFrames,
              loaded: true
            };
          } catch (error) {
            console.error(`Error loading ${clip.src}:`, error);
            return {
              ...clip,
              durationInSeconds: 10, // Fallback
              durationInFrames: 300, // Fallback
              loaded: false
            };
          }
        })
      );

      setVideoClips(updatedClips);
      const total = updatedClips.reduce((sum, clip) => sum + clip.durationInFrames, 0);
      setTotalDurationInFrames(total);
      setLoading(false);
    };

    loadAllVideoDurations();
  }, [fps]);

  return {
    videoClips,
    loading,
    totalDurationInFrames,
    totalDurationInSeconds: totalDurationInFrames / fps
  };
};
