import { Video } from 'remotion';
import { useState } from 'react';

interface VideoClipProps {
  src: string;
  startFrom?: number;
  endAt?: number;
}

export const VideoClip: React.FC<VideoClipProps> = ({
  src,
  startFrom = 0,
  endAt
}) => {
  const [error, setError] = useState<string | null>(null);
  const [loaded, setLoaded] = useState(false);

  const handleError = () => {
    setError(`Failed to load video: ${src}`);
    console.error(`Failed to load video: ${src}`);
  };

  const handleLoad = () => {
    setLoaded(true);
    console.log(`Successfully loaded video: ${src}`);
  };

  if (error) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f0f0f0',
        color: '#666',
        fontSize: '14px',
        textAlign: 'center',
        padding: '20px'
      }}>
        <div>
          <div>❌ Video Load Error</div>
          <div style={{ fontSize: '12px', marginTop: '5px' }}>
            {src}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {!loaded && (
        <div style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f0f0f0',
          color: '#666',
          fontSize: '14px'
        }}>
          Loading video: {src}
        </div>
      )}
      <Video
        src={src}
        startFrom={startFrom}
        endAt={endAt}
        onError={handleError}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: loaded ? 1 : 0.1
        }}
        muted
        playsInline
        onLoadedData={handleLoad}
      />
    </>
  );
};
