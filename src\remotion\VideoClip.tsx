import { Video } from 'remotion';

interface VideoClipProps {
  src: string;
  startFrom?: number;
  endAt?: number;
}

export const VideoClip: React.FC<VideoClipProps> = ({ 
  src, 
  startFrom = 0, 
  endAt 
}) => {
  return (
    <Video
      src={src}
      startFrom={startFrom}
      endAt={endAt}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      }}
    />
  );
};
