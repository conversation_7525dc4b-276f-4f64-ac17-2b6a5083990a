import { Sequence, useCurrentFrame } from 'remotion';
import { VideoClip } from './VideoClip';
import { useEffect } from 'react';

// Hardcoded video clips for demonstration
const videoClips = [
  {
    src: '/videos/clip1.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 1'
  },
  {
    src: '/videos/clip2.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 2'
  },
  {
    src: '/videos/clip3.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 3'
  }
];

export const VideoStitching: React.FC = () => {
  const frame = useCurrentFrame();
  let currentFrame = 0;

  useEffect(() => {
    console.log('VideoStitching component mounted');
    console.log('Video clips configuration:', videoClips);
  }, []);

  useEffect(() => {
    console.log('Current frame:', frame);
  }, [frame]);

  return (
    <div style={{
      flex: 1,
      backgroundColor: 'black',
      position: 'relative',
      width: '100%',
      height: '100%'
    }}>
      {videoClips.map((clip, index) => {
        const sequence = (
          <Sequence
            key={index}
            from={currentFrame}
            durationInFrames={clip.durationInFrames}
          >
            <VideoClip src={clip.src} />
          </Sequence>
        );

        console.log(`Sequence ${index}: from ${currentFrame} to ${currentFrame + clip.durationInFrames}`);
        currentFrame += clip.durationInFrames;
        return sequence;
      })}

      {/* Debug overlay */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        color: 'white',
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: '5px 10px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 1000
      }}>
        Frame: {frame} / 900
      </div>
    </div>
  );
};
