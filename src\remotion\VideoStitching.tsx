import { Sequence } from 'remotion';
import { VideoClip } from './VideoClip';

// Hardcoded video clips for demonstration
const videoClips = [
  {
    src: '/videos/clip1.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 1'
  },
  {
    src: '/videos/clip2.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 2'
  },
  {
    src: '/videos/clip3.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 3'
  }
];

export const VideoStitching: React.FC = () => {
  let currentFrame = 0;

  return (
    <div style={{ flex: 1, backgroundColor: 'black' }}>
      {videoClips.map((clip, index) => {
        const sequence = (
          <Sequence
            key={index}
            from={currentFrame}
            durationInFrames={clip.durationInFrames}
          >
            <VideoClip src={clip.src} />
          </Sequence>
        );
        
        currentFrame += clip.durationInFrames;
        return sequence;
      })}
    </div>
  );
};
