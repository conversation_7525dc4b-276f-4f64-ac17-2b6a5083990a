import { Sequence, useCurrentFrame, useVideoConfig } from 'remotion';
import { VideoClip } from './VideoClip';
import { useEffect, useState } from 'react';

interface VideoClipData {
  src: string;
  durationInFrames: number;
  durationInSeconds: number;
  name: string;
  loaded: boolean;
}

// Initial video clips with placeholder durations
const initialVideoClips = [
  {
    src: '/videos/clip1.mp4',
    durationInFrames: 300, // Will be updated with actual duration
    durationInSeconds: 10, // Will be updated with actual duration
    name: 'Clip 1',
    loaded: false
  },
  {
    src: '/videos/clip2.mp4',
    durationInFrames: 300, // Will be updated with actual duration
    durationInSeconds: 10, // Will be updated with actual duration
    name: 'Clip 2',
    loaded: false
  },
  {
    src: '/videos/clip3.mp4',
    durationInFrames: 300, // Will be updated with actual duration
    durationInSeconds: 10, // Will be updated with actual duration
    name: 'Clip 3',
    loaded: false
  }
];

export const VideoStitching: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const [videoClips, setVideoClips] = useState<VideoClipData[]>(initialVideoClips);
  const [allVideosLoaded, setAllVideosLoaded] = useState(false);

  // Function to get video duration
  const getVideoDuration = (src: string): Promise<number> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        const duration = video.duration;
        console.log(`Video ${src} duration: ${duration} seconds`);
        resolve(duration);
      };

      video.onerror = () => {
        console.error(`Failed to load video metadata for ${src}`);
        reject(new Error(`Failed to load video: ${src}`));
      };

      video.src = src;
    });
  };

  // Load video durations on component mount
  useEffect(() => {
    console.log('VideoStitching component mounted');
    console.log('Loading video durations...');

    const loadAllVideoDurations = async () => {
      const updatedClips = await Promise.all(
        initialVideoClips.map(async (clip) => {
          try {
            const durationInSeconds = await getVideoDuration(clip.src);
            const durationInFrames = Math.round(durationInSeconds * fps);

            return {
              ...clip,
              durationInSeconds,
              durationInFrames,
              loaded: true
            };
          } catch (error) {
            console.error(`Error loading ${clip.src}:`, error);
            return {
              ...clip,
              loaded: false
            };
          }
        })
      );

      setVideoClips(updatedClips);
      setAllVideosLoaded(true);
      console.log('All video durations loaded:', updatedClips);
    };

    loadAllVideoDurations();
  }, [fps]);

  useEffect(() => {
    if (frame % 30 === 0) { // Log every second
      console.log('Current frame:', frame);
    }
  }, [frame]);

  // Calculate total duration
  const totalDurationInFrames = videoClips.reduce((total, clip) => total + clip.durationInFrames, 0);
  const totalDurationInSeconds = videoClips.reduce((total, clip) => total + clip.durationInSeconds, 0);

  // Show loading state if videos aren't loaded yet
  if (!allVideosLoaded) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        backgroundColor: 'black',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '16px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div>🎬 Loading video durations...</div>
          <div style={{ fontSize: '12px', marginTop: '10px', opacity: 0.7 }}>
            Analyzing video files to get actual durations
          </div>
        </div>
      </div>
    );
  }

  let currentFrame = 0;

  return (
    <div style={{
      flex: 1,
      backgroundColor: 'black',
      position: 'relative',
      width: '100%',
      height: '100%'
    }}>
      {videoClips.map((clip, index) => {
        if (!clip.loaded) {
          return null; // Skip clips that failed to load
        }

        const sequence = (
          <Sequence
            key={index}
            from={currentFrame}
            durationInFrames={clip.durationInFrames}
          >
            <VideoClip src={clip.src} />
          </Sequence>
        );

        console.log(`Sequence ${index}: from ${currentFrame} to ${currentFrame + clip.durationInFrames} (${clip.durationInSeconds.toFixed(1)}s)`);
        currentFrame += clip.durationInFrames;
        return sequence;
      })}

      {/* Enhanced debug overlay */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        color: 'white',
        backgroundColor: 'rgba(0,0,0,0.8)',
        padding: '8px 12px',
        borderRadius: '6px',
        fontSize: '12px',
        zIndex: 1000,
        fontFamily: 'monospace'
      }}>
        <div>Frame: {frame} / {totalDurationInFrames}</div>
        <div>Time: {(frame / fps).toFixed(1)}s / {totalDurationInSeconds.toFixed(1)}s</div>
        <div style={{ marginTop: '4px', fontSize: '10px', opacity: 0.8 }}>
          {videoClips.map((clip, i) => (
            <div key={i}>
              {clip.name}: {clip.durationInSeconds.toFixed(1)}s ({clip.durationInFrames}f)
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

