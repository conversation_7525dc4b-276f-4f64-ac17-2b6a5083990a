import { Sequence, useCurrentFrame, useVideoConfig } from 'remotion';
import { VideoClip } from './VideoClip';
import { useEffect } from 'react';

// Hardcoded video clips for demonstration
const videoClips = [
  {
    src: '/videos/clip1.mp4',
    durationInFrames: 309, // 10.3 seconds at 30fps
    durationInSeconds: 10.3,
    name: 'Clip 1'
  },
  {
    src: '/videos/clip2.mp4',
    durationInFrames: 309, // 10.3 seconds at 30fps
    durationInSeconds: 10.3,
    name: 'Clip 2'
  },
  {
    src: '/videos/clip3.mp4',
    durationInFrames: 309, // 10.3 seconds at 30fps
    durationInSeconds: 10.3,
    name: 'Clip 3'
  }
];

export const VideoStitching: React.FC = () => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  useEffect(() => {
    console.log('VideoStitching component mounted');
    console.log('Video clips configuration:', videoClips);
  }, []);

  useEffect(() => {
    if (frame % 30 === 0) { // Log every second
      console.log('Current frame:', frame);
    }
  }, [frame]);

  // Calculate total duration
  const totalDurationInFrames = videoClips.reduce((total, clip) => total + clip.durationInFrames, 0);
  const totalDurationInSeconds = videoClips.reduce((total, clip) => total + clip.durationInSeconds, 0);

  let currentFrame = 0;

  return (
    <div style={{
      flex: 1,
      backgroundColor: 'black',
      position: 'relative',
      width: '100%',
      height: '100%'
    }}>
      {videoClips.map((clip, index) => {
        const sequence = (
          <Sequence
            key={index}
            from={currentFrame}
            durationInFrames={clip.durationInFrames}
          >
            <VideoClip src={clip.src} />
          </Sequence>
        );

        console.log(`Sequence ${index}: from ${currentFrame} to ${currentFrame + clip.durationInFrames} (${clip.durationInSeconds.toFixed(1)}s)`);
        currentFrame += clip.durationInFrames;
        return sequence;
      })}

      {/* Enhanced debug overlay */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        color: 'white',
        backgroundColor: 'rgba(0,0,0,0.8)',
        padding: '8px 12px',
        borderRadius: '6px',
        fontSize: '12px',
        zIndex: 1000,
        fontFamily: 'monospace'
      }}>
        <div>Frame: {frame} / {totalDurationInFrames}</div>
        <div>Time: {(frame / fps).toFixed(1)}s / {totalDurationInSeconds.toFixed(1)}s</div>
        <div style={{ marginTop: '4px', fontSize: '10px', opacity: 0.8 }}>
          {videoClips.map((clip, i) => (
            <div key={i}>
              {clip.name}: {clip.durationInSeconds.toFixed(1)}s ({clip.durationInFrames}f)
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

