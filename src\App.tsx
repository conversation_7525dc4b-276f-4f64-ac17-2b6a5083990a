import { Player } from '@remotion/player';
import { VideoStitching } from './remotion/VideoStitching';
import './App.css';

function App() {
  return (
    <div className="app-container">
      <header className="app-header">
        <h1 className="app-title">Remotion Video Editor</h1>
        <p className="app-subtitle">Phase 1: Basic Video Stitching Demo</p>
      </header>

      <div className="player-container">
        <Player
          component={VideoStitching}
          durationInFrames={900}
          compositionWidth={1920}
          compositionHeight={1080}
          fps={30}
          style={{
            width: '100%',
            height: 'auto',
            aspectRatio: '16/9'
          }}
          controls
          showVolumeControls
          clickToPlay
        />
      </div>

      <div className="instructions-section">
        <h3 className="instructions-title">
          Instructions
          <span className="status-indicator status-success">✓ Videos Found</span>
        </h3>
        <p className="instructions-text">
          To test the video stitching functionality, please add the following sample video files to the <code>public/videos/</code> directory:
        </p>
        <ul className="instructions-list">
          <li><code>clip1.mp4</code> - First video clip (10 seconds)</li>
          <li><code>clip2.mp4</code> - Second video clip (10 seconds)</li>
          <li><code>clip3.mp4</code> - Third video clip (10 seconds)</li>
        </ul>
        <p className="instructions-text">
          The videos will be automatically stitched together in sequence for a total duration of 30 seconds.
        </p>
      </div>
    </div>
  );
}

export default App;
