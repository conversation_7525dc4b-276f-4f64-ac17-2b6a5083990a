import { Player } from '@remotion/player';
import { VideoStitching } from './remotion/VideoStitching';
import './App.css';

function App() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>Remotion Video Editor</h1>
      <p>Phase 1: Basic Video Stitching Demo</p>

      <div style={{
        marginTop: '20px',
        border: '2px solid #ccc',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <Player
          component={VideoStitching}
          durationInFrames={900}
          compositionWidth={1920}
          compositionHeight={1080}
          fps={30}
          style={{
            width: '100%',
            maxWidth: '800px',
            height: 'auto'
          }}
          controls
        />
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Instructions:</h3>
        <p>To test the video stitching functionality, please add the following sample video files to the <code>public/videos/</code> directory:</p>
        <ul>
          <li><code>clip1.mp4</code> - First video clip (10 seconds)</li>
          <li><code>clip2.mp4</code> - Second video clip (10 seconds)</li>
          <li><code>clip3.mp4</code> - Third video clip (10 seconds)</li>
        </ul>
        <p>The videos will be automatically stitched together in sequence for a total duration of 30 seconds.</p>
      </div>
    </div>
  );
}

export default App;
